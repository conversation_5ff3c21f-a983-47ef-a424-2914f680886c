// Layer Type Enum header
//
// This file is auto-generated by cmake, don't edit it.

AbsVal = 0,
ArgMax = 1,
BatchNorm = 2,
Bias = 3,
BNLL = 4,
Concat = 5,
Convolution = 6,
Crop = 7,
Deconvolution = 8,
Dropout = 9,
Eltwise = 10,
ELU = 11,
Embed = 12,
Exp = 13,
Flatten = 14,
InnerProduct = 15,
Input = 16,
Log = 17,
LRN = 18,
MemoryData = 19,
MVN = 20,
Pooling = 21,
Power = 22,
PReLU = 23,
Proposal = 24,
Reduction = 25,
ReLU = 26,
Reshape = 27,
ROIPooling = 28,
Scale = 29,
Sigmoid = 30,
Slice = 31,
Softmax = 32,
Split = 33,
SPP = 34,
TanH = 35,
Threshold = 36,
Tile = 37,
RNN = 38,
LSTM = 39,
BinaryOp = 40,
UnaryOp = 41,
ConvolutionDepthWise = 42,
Padding = 43,
Squeeze = 44,
ExpandDims = 45,
Normalize = 46,
Permute = 47,
PriorBox = 48,
DetectionOutput = 49,
Interp = 50,
DeconvolutionDepthWise = 51,
ShuffleChannel = 52,
InstanceNorm = 53,
Clip = 54,
Reorg = 55,
YoloDetectionOutput = 56,
Quantize = 57,
Dequantize = 58,
Yolov3DetectionOutput = 59,
PSROIPooling = 60,
ROIAlign = 61,
Packing = 62,
Requantize = 63,
Cast = 64,
HardSigmoid = 65,
SELU = 66,
HardSwish = 67,
Noop = 68,
PixelShuffle = 69,
DeepCopy = 70,
Mish = 71,
StatisticsPooling = 72,
Swish = 73,
Gemm = 74,
GroupNorm = 75,
LayerNorm = 76,
Softplus = 77,
GRU = 78,
MultiHeadAttention = 79,
GELU = 80,
Convolution1D = 81,
Pooling1D = 82,
ConvolutionDepthWise1D = 83,
Convolution3D = 84,
ConvolutionDepthWise3D = 85,
Pooling3D = 86,
MatMul = 87,
Deconvolution1D = 88,
DeconvolutionDepthWise1D = 89,
Deconvolution3D = 90,
DeconvolutionDepthWise3D = 91,
Einsum = 92,
DeformableConv2D = 93,
GLU = 94,
Fold = 95,
Unfold = 96,
GridSample = 97,

