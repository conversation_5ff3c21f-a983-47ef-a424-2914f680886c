<resources>
    <string name="app_name">DUIX Demo</string>
    <string name="play">播放</string>
    <string name="base_config_url">基础配置Url:</string>
    <string name="model_url">模型Url:</string>
    <string name="more">更多</string>
    <string name="base_config_cannot_be_empty">基础配置url不能为空</string>
    <string name="model_url_cannot_be_empty">模型url不能为空</string>
    <string name="loading">加载中...</string>
    <string name="mute">静音</string>
    <string name="ai_tips">本内容由AI生成，仅供参考</string>
    <string name="support_actions">支持的动作:</string>
    <string name="record">录音</string>
    <string name="play_pcm">播放PCM流</string>
    <string name="play_wav">播放WAV文件</string>
    <string name="random_play_action">随机播放动作</string>
    <string name="touch_me_record">按住我收音</string>
    <string name="need_permission_continue">需要授权以继续使用</string>
    <string name="play_stop">停止播放</string>
    <string name="main_download_tips">Github地址可能下载失败，您可以考虑使用代理或者将文件缓存到自己的存储服务</string>
    <string name="debug_message">调试信息</string>

</resources>