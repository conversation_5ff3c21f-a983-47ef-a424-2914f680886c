<resources>
    <string name="app_name">DUIX Demo</string>
    <string name="play">Play</string>
    <string name="base_config_url">BaseConfig Url:</string>
    <string name="model_url">Model Url:</string>
    <string name="more">More</string>
    <string name="base_config_cannot_be_empty">BaseConfig url cannot be empty</string>
    <string name="model_url_cannot_be_empty">Model url cannot be empty</string>
    <string name="loading">Loading...</string>
    <string name="mute">Mute</string>
    <string name="ai_tips">This content is generated by AI and is for reference only</string>
    <string name="support_actions">Supported actions:</string>
    <string name="record">Record</string>
    <string name="play_pcm">Play PCM stream</string>
    <string name="play_wav">Play WAV file</string>
    <string name="random_play_action">Random play action</string>
    <string name="touch_me_record">Press and hold</string>
    <string name="need_permission_continue">Permission is required to continue using</string>
    <string name="play_stop">Stop playing</string>
    <string name="main_download_tips">The GitHub url may fail to download, you can consider using a proxy or caching the file to your own storage service</string>
    <string name="debug_message">Debug message</string>
</resources>