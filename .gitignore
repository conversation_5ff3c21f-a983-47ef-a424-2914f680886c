# Duix Mobile Android Project .gitignore

# Built application files
*.apk
*.ap_
*.aab

# Files for the ART/Dalvik VM
*.dex

# Java class files
*.class

# Generated files
bin/
gen/
out/
build/
*/build/
*/*/build/
*/debug/
*/release/

# Gradle files
.gradle/
gradlew
gradlew.bat

# Local configuration file (sdk path, etc)
local.properties

# Proguard folder generated by Eclipse
proguard/

# Log Files
*.log

# Android Studio Navigation editor temp files
.navigation/

# Android Studio captures folder
captures/
/captures

# IntelliJ
*.iml
.idea/
/.idea/
.idea/workspace.xml
.idea/tasks.xml
.idea/gradle.xml
.idea/assetWizardSettings.xml
.idea/dictionaries
.idea/libraries
.idea/caches
.idea/modules.xml
.idea/navEditor.xml

# External native build folder generated in Android Studio 2.2 and later
.externalNativeBuild
.cxx/

# NDK
obj/

# CMake
cmake-build-*/

# Android Profiling
*.hprof

# Android lint
lint/intermediates/
lint/generated/
lint/outputs/
lint/tmp/
lint-results.xml
lint-results_files/
lint-baseline.xml

# Unit test reports
TEST-*.xml

# Duix Mobile Specific Files
# ===========================

# Model files (large binary files - uncomment if needed)
# *.zip
# models/
# assets/models/
# duix-models/

# Audio test files
*.wav
*.pcm
assets/audio/
assets/pcm/

# Generated AAR files
*.aar

# Duix SDK compiled outputs
duix-sdk/build/
duix-sdk/.cxx/

# Test app outputs
test/build/
test/.cxx/

# Demo keystore (uncomment if using custom keystore)
# demo.jks

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~
~$*
*.bat

# IDE files
.vscode/
*.sublime-project
*.sublime-workspace

# Backup files
*.bak
*.backup

# Memory dumps
*.hprof
*.dump

# Crash logs
crash/
crashes/

# Performance profiling
*.trace

# Output metadata
output-metadata.json

# Secrets and API keys
secrets.properties
api-keys.properties

# Cache directories
.cache/
cache/

# Temporary build files
tmp/
temp/

# Firebase (if used)
google-services.json
GoogleService-Info.plist

# Large media files (uncomment if needed)
# *.mp4
# *.mov
# *.avi
# *.mkv
# *.webm

# Database files
*.db
*.sqlite
*.sqlite3

# JetBrains IDEs
*.iws
*.ipr

# Eclipse
.metadata
.recommenders/
.settings/
.project
.classpath

# NetBeans
nbproject/private/
nbbuild/
nbdist/
.nb-gradle/

# Visual Studio Code
.vscode/

# Emacs
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Vim
.netrwhist

# Sublime Text
*.tmproj
*.tmproject
tmtags

# JEnv
.java-version

# Maven (if used)
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# Environment variables
.env
.env.local
.env.*.local

# Documentation build
docs/build/
wiki/build/

# Package files (uncomment if needed)
# *.tar.gz
# *.tgz
# *.rar
# *.7z

# Generated by Windows
Desktop.ini

# Generated by Linux
*~

# Fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots
fastlane/test_output
fastlane/readme.md

# Freeline
freeline.py
freeline/
freeline_project_description.json

# Version control
vcs.xml

# Google Services (uncomment if not using Firebase)
# google-services.json

# Keystore files (uncomment if you don't want to track keystores)
# *.jks
# *.keystore

# Gradle Wrapper (uncomment if you want to ignore wrapper - not recommended)
# gradle/wrapper/gradle-wrapper.jar
# gradle/wrapper/gradle-wrapper.properties
