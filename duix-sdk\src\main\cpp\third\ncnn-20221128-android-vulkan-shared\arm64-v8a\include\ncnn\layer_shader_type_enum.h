// Layer Shader Enum header
//
// This file is auto-generated by cmake, don't edit it.

absval = 0,
absval_pack4 = 1,
absval_pack8 = 2,
batchnorm = 3,
batchnorm_pack4 = 4,
batchnorm_pack8 = 5,
concat = 6,
concat_pack4 = 7,
concat_pack4to1 = 8,
concat_pack8 = 9,
concat_pack8to1 = 10,
concat_pack8to4 = 11,
convolution = 12,
convolution_1x1s1d1 = 13,
convolution_3x3s1d1_winograd23_transform_input = 14,
convolution_3x3s1d1_winograd23_transform_output = 15,
convolution_3x3s1d1_winograd43_transform_input = 16,
convolution_3x3s1d1_winograd43_transform_output = 17,
convolution_3x3s1d1_winograd_gemm = 18,
convolution_gemm = 19,
convolution_pack1to4 = 20,
convolution_pack1to4_1x1s1d1 = 21,
convolution_pack1to4_3x3s1d1_winograd_gemm = 22,
convolution_pack1to4_gemm = 23,
convolution_pack1to8 = 24,
convolution_pack1to8_1x1s1d1 = 25,
convolution_pack1to8_3x3s1d1_winograd_gemm = 26,
convolution_pack1to8_gemm = 27,
convolution_pack4 = 28,
convolution_pack4_1x1s1d1 = 29,
convolution_pack4_1x1s1d1_cm_16_8_8 = 30,
convolution_pack4_3x3s1d1_winograd23_transform_input = 31,
convolution_pack4_3x3s1d1_winograd23_transform_output = 32,
convolution_pack4_3x3s1d1_winograd43_transform_input = 33,
convolution_pack4_3x3s1d1_winograd43_transform_output = 34,
convolution_pack4_3x3s1d1_winograd_gemm = 35,
convolution_pack4_3x3s1d1_winograd_gemm_cm_16_8_8 = 36,
convolution_pack4_gemm = 37,
convolution_pack4_gemm_cm_16_8_8 = 38,
convolution_pack4to1 = 39,
convolution_pack4to1_1x1s1d1 = 40,
convolution_pack4to1_3x3s1d1_winograd_gemm = 41,
convolution_pack4to1_gemm = 42,
convolution_pack4to8 = 43,
convolution_pack4to8_1x1s1d1 = 44,
convolution_pack4to8_3x3s1d1_winograd_gemm = 45,
convolution_pack4to8_gemm = 46,
convolution_pack8 = 47,
convolution_pack8_1x1s1d1 = 48,
convolution_pack8_3x3s1d1_winograd23_transform_input = 49,
convolution_pack8_3x3s1d1_winograd23_transform_output = 50,
convolution_pack8_3x3s1d1_winograd43_transform_input = 51,
convolution_pack8_3x3s1d1_winograd43_transform_output = 52,
convolution_pack8_3x3s1d1_winograd_gemm = 53,
convolution_pack8_gemm = 54,
convolution_pack8to1 = 55,
convolution_pack8to1_1x1s1d1 = 56,
convolution_pack8to1_3x3s1d1_winograd_gemm = 57,
convolution_pack8to1_gemm = 58,
convolution_pack8to4 = 59,
convolution_pack8to4_1x1s1d1 = 60,
convolution_pack8to4_3x3s1d1_winograd_gemm = 61,
convolution_pack8to4_gemm = 62,
crop = 63,
crop_pack1to4 = 64,
crop_pack1to8 = 65,
crop_pack4 = 66,
crop_pack4to1 = 67,
crop_pack4to8 = 68,
crop_pack8 = 69,
crop_pack8to1 = 70,
crop_pack8to4 = 71,
deconvolution = 72,
deconvolution_col2im = 73,
deconvolution_gemm = 74,
deconvolution_pack1to4 = 75,
deconvolution_pack1to4_gemm = 76,
deconvolution_pack1to8 = 77,
deconvolution_pack1to8_gemm = 78,
deconvolution_pack4 = 79,
deconvolution_pack4_col2im = 80,
deconvolution_pack4_gemm = 81,
deconvolution_pack4_gemm_cm_16_8_8 = 82,
deconvolution_pack4to1 = 83,
deconvolution_pack4to1_gemm = 84,
deconvolution_pack4to8 = 85,
deconvolution_pack4to8_gemm = 86,
deconvolution_pack8 = 87,
deconvolution_pack8_col2im = 88,
deconvolution_pack8_gemm = 89,
deconvolution_pack8to1 = 90,
deconvolution_pack8to1_gemm = 91,
deconvolution_pack8to4 = 92,
deconvolution_pack8to4_gemm = 93,
dropout = 94,
dropout_pack4 = 95,
dropout_pack8 = 96,
eltwise = 97,
eltwise_pack4 = 98,
eltwise_pack8 = 99,
elu = 100,
elu_pack4 = 101,
elu_pack8 = 102,
flatten = 103,
flatten_pack1to4 = 104,
flatten_pack1to8 = 105,
flatten_pack4 = 106,
flatten_pack4to8 = 107,
flatten_pack8 = 108,
innerproduct = 109,
innerproduct_gemm = 110,
innerproduct_gemm_wp1to4 = 111,
innerproduct_gemm_wp1to8 = 112,
innerproduct_gemm_wp4 = 113,
innerproduct_gemm_wp4to1 = 114,
innerproduct_gemm_wp4to8 = 115,
innerproduct_gemm_wp8 = 116,
innerproduct_gemm_wp8to1 = 117,
innerproduct_gemm_wp8to4 = 118,
innerproduct_pack1to4 = 119,
innerproduct_pack1to8 = 120,
innerproduct_pack4 = 121,
innerproduct_pack4to1 = 122,
innerproduct_pack4to8 = 123,
innerproduct_pack8 = 124,
innerproduct_pack8to1 = 125,
innerproduct_pack8to4 = 126,
innerproduct_reduce_sum8 = 127,
innerproduct_reduce_sum8_pack4 = 128,
innerproduct_reduce_sum8_pack8 = 129,
innerproduct_sum8 = 130,
innerproduct_sum8_pack1to4 = 131,
innerproduct_sum8_pack1to8 = 132,
innerproduct_sum8_pack4 = 133,
innerproduct_sum8_pack4to1 = 134,
innerproduct_sum8_pack4to8 = 135,
innerproduct_sum8_pack8 = 136,
innerproduct_sum8_pack8to1 = 137,
innerproduct_sum8_pack8to4 = 138,
lrn_norm = 139,
lrn_norm_across_channel_pack4 = 140,
lrn_norm_across_channel_pack8 = 141,
lrn_norm_within_channel_pack4 = 142,
lrn_norm_within_channel_pack8 = 143,
lrn_square_pad = 144,
lrn_square_pad_across_channel_pack4 = 145,
lrn_square_pad_across_channel_pack8 = 146,
lrn_square_pad_within_channel_pack4 = 147,
lrn_square_pad_within_channel_pack8 = 148,
pooling = 149,
pooling_adaptive = 150,
pooling_adaptive_pack4 = 151,
pooling_adaptive_pack8 = 152,
pooling_global = 153,
pooling_global_pack4 = 154,
pooling_global_pack8 = 155,
pooling_pack4 = 156,
pooling_pack8 = 157,
prelu = 158,
prelu_pack4 = 159,
prelu_pack8 = 160,
relu = 161,
relu_pack4 = 162,
relu_pack8 = 163,
reshape = 164,
reshape_pack1to4 = 165,
reshape_pack1to8 = 166,
reshape_pack4 = 167,
reshape_pack4to1 = 168,
reshape_pack4to8 = 169,
reshape_pack8 = 170,
reshape_pack8to1 = 171,
reshape_pack8to4 = 172,
scale = 173,
scale_pack4 = 174,
scale_pack8 = 175,
sigmoid = 176,
sigmoid_pack4 = 177,
sigmoid_pack8 = 178,
slice = 179,
slice_pack1to4 = 180,
slice_pack1to8 = 181,
slice_pack4 = 182,
slice_pack4to8 = 183,
slice_pack8 = 184,
softmax_div_sum = 185,
softmax_div_sum_pack4 = 186,
softmax_div_sum_pack8 = 187,
softmax_exp_sub_max = 188,
softmax_exp_sub_max_pack4 = 189,
softmax_exp_sub_max_pack8 = 190,
softmax_reduce_max = 191,
softmax_reduce_max_pack4 = 192,
softmax_reduce_max_pack8 = 193,
softmax_reduce_sum = 194,
softmax_reduce_sum_pack4 = 195,
softmax_reduce_sum_pack8 = 196,
tanh = 197,
tanh_pack4 = 198,
tanh_pack8 = 199,
binaryop = 200,
binaryop_broadcast = 201,
binaryop_broadcast_a1_pack4 = 202,
binaryop_broadcast_a1_pack8 = 203,
binaryop_broadcast_b1_pack4 = 204,
binaryop_broadcast_b1_pack8 = 205,
binaryop_broadcast_pack4 = 206,
binaryop_broadcast_pack8 = 207,
binaryop_pack4 = 208,
binaryop_pack8 = 209,
unaryop = 210,
unaryop_pack4 = 211,
unaryop_pack8 = 212,
convolutiondepthwise = 213,
convolutiondepthwise_group = 214,
convolutiondepthwise_group_pack1to4 = 215,
convolutiondepthwise_group_pack1to8 = 216,
convolutiondepthwise_group_pack4 = 217,
convolutiondepthwise_group_pack4to1 = 218,
convolutiondepthwise_group_pack4to8 = 219,
convolutiondepthwise_group_pack8 = 220,
convolutiondepthwise_group_pack8to1 = 221,
convolutiondepthwise_group_pack8to4 = 222,
convolutiondepthwise_pack4 = 223,
convolutiondepthwise_pack8 = 224,
padding = 225,
padding_3d = 226,
padding_3d_pack4 = 227,
padding_3d_pack8 = 228,
padding_pack1to4 = 229,
padding_pack1to8 = 230,
padding_pack4 = 231,
padding_pack4to1 = 232,
padding_pack4to8 = 233,
padding_pack8 = 234,
padding_pack8to1 = 235,
padding_pack8to4 = 236,
normalize_coeffs = 237,
normalize_coeffs_pack4 = 238,
normalize_coeffs_pack8 = 239,
normalize_norm = 240,
normalize_norm_pack4 = 241,
normalize_norm_pack8 = 242,
normalize_reduce_sum4_fp16_to_fp32 = 243,
normalize_reduce_sum4_fp16_to_fp32_pack4 = 244,
normalize_reduce_sum4_fp16_to_fp32_pack8 = 245,
normalize_reduce_sum4_fp32 = 246,
normalize_reduce_sum4_fp32_pack4 = 247,
normalize_reduce_sum4_fp32_pack8 = 248,
permute = 249,
permute_pack1to4 = 250,
permute_pack1to8 = 251,
permute_pack4 = 252,
permute_pack4to1 = 253,
permute_pack4to8 = 254,
permute_pack8 = 255,
permute_pack8to1 = 256,
permute_pack8to4 = 257,
priorbox = 258,
priorbox_mxnet = 259,
interp = 260,
interp_bicubic = 261,
interp_bicubic_coeffs = 262,
interp_bicubic_pack4 = 263,
interp_bicubic_pack8 = 264,
interp_pack4 = 265,
interp_pack8 = 266,
deconvolutiondepthwise = 267,
deconvolutiondepthwise_group = 268,
deconvolutiondepthwise_group_pack1to4 = 269,
deconvolutiondepthwise_group_pack1to8 = 270,
deconvolutiondepthwise_group_pack4 = 271,
deconvolutiondepthwise_group_pack4to1 = 272,
deconvolutiondepthwise_group_pack4to8 = 273,
deconvolutiondepthwise_group_pack8 = 274,
deconvolutiondepthwise_group_pack8to1 = 275,
deconvolutiondepthwise_group_pack8to4 = 276,
deconvolutiondepthwise_pack4 = 277,
deconvolutiondepthwise_pack8 = 278,
shufflechannel = 279,
shufflechannel_pack4 = 280,
shufflechannel_pack8 = 281,
instancenorm_coeffs = 282,
instancenorm_coeffs_pack4 = 283,
instancenorm_coeffs_pack8 = 284,
instancenorm_norm = 285,
instancenorm_norm_pack4 = 286,
instancenorm_norm_pack8 = 287,
instancenorm_reduce_mean = 288,
instancenorm_reduce_mean_pack4 = 289,
instancenorm_reduce_mean_pack8 = 290,
instancenorm_reduce_sum4_fp16_to_fp32 = 291,
instancenorm_reduce_sum4_fp16_to_fp32_pack4 = 292,
instancenorm_reduce_sum4_fp16_to_fp32_pack8 = 293,
instancenorm_reduce_sum4_fp32 = 294,
instancenorm_reduce_sum4_fp32_pack4 = 295,
instancenorm_reduce_sum4_fp32_pack8 = 296,
instancenorm_sub_mean_square = 297,
instancenorm_sub_mean_square_pack4 = 298,
instancenorm_sub_mean_square_pack8 = 299,
clip = 300,
clip_pack4 = 301,
clip_pack8 = 302,
reorg = 303,
reorg_pack1to4 = 304,
reorg_pack1to8 = 305,
reorg_pack4 = 306,
reorg_pack4to8 = 307,
reorg_pack8 = 308,
packing = 309,
packing_fp16_to_fp32 = 310,
packing_fp32_to_fp16 = 311,
packing_pack1to4 = 312,
packing_pack1to4_fp16_to_fp32 = 313,
packing_pack1to4_fp32_to_fp16 = 314,
packing_pack1to8 = 315,
packing_pack1to8_fp16_to_fp32 = 316,
packing_pack1to8_fp32_to_fp16 = 317,
packing_pack4 = 318,
packing_pack4_fp16_to_fp32 = 319,
packing_pack4_fp32_to_fp16 = 320,
packing_pack4to1 = 321,
packing_pack4to1_fp16_to_fp32 = 322,
packing_pack4to1_fp32_to_fp16 = 323,
packing_pack4to8 = 324,
packing_pack4to8_fp16_to_fp32 = 325,
packing_pack4to8_fp32_to_fp16 = 326,
packing_pack8 = 327,
packing_pack8_fp16_to_fp32 = 328,
packing_pack8_fp32_to_fp16 = 329,
packing_pack8to1 = 330,
packing_pack8to1_fp16_to_fp32 = 331,
packing_pack8to1_fp32_to_fp16 = 332,
packing_pack8to4 = 333,
packing_pack8to4_fp16_to_fp32 = 334,
packing_pack8to4_fp32_to_fp16 = 335,
cast_fp16_to_fp32 = 336,
cast_fp16_to_fp32_pack4 = 337,
cast_fp16_to_fp32_pack8 = 338,
cast_fp32_to_fp16 = 339,
cast_fp32_to_fp16_pack4 = 340,
cast_fp32_to_fp16_pack8 = 341,
hardsigmoid = 342,
hardsigmoid_pack4 = 343,
hardsigmoid_pack8 = 344,
hardswish = 345,
hardswish_pack4 = 346,
hardswish_pack8 = 347,
pixelshuffle = 348,
pixelshuffle_pack4 = 349,
pixelshuffle_pack4to1 = 350,
pixelshuffle_pack8 = 351,
pixelshuffle_pack8to1 = 352,
pixelshuffle_pack8to4 = 353,
deepcopy = 354,
deepcopy_pack4 = 355,
deepcopy_pack8 = 356,
mish = 357,
mish_pack4 = 358,
mish_pack8 = 359,
swish = 360,
swish_pack4 = 361,
swish_pack8 = 362,
convert_ycbcr = 363,
vulkan_activation = 364,

