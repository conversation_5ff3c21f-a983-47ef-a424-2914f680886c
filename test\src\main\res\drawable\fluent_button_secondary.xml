<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/fluent_neutral_96" />
            <stroke android:color="@color/fluent_neutral_60" android:width="1dp" />
            <corners android:radius="4dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/fluent_surface" />
            <stroke android:color="@color/fluent_neutral_60" android:width="1dp" />
            <corners android:radius="4dp" />
        </shape>
    </item>
</selector>