# Duix Mobile Wiki 文档中心

欢迎来到 Duix Mobile 项目的 Wiki 文档中心！这里包含了项目开发、部署和使用的详细文档。

## 📚 文档目录

### 📖 项目文档
- [项目概览](./项目概览.md) - 项目技术架构和核心功能介绍

### 🛠 开发环境
- [Java环境要求](./Java环境要求.md) - 详细的Java开发环境配置指南

### 📱 平台文档
- [Android SDK文档](../README.md) - Android平台开发指南
- [iOS SDK文档](../duix-ios/GJLocalDigitalDemo/README.md) - iOS平台开发指南

### 📖 技术文档
- [接口参考手册](../Interface%20Reference%20Handbook_EN.pdf) - API接口详细说明
- [许可证文档](../Guiji-duix.ai-sdk-1.0.3%20Model%20Community%20License_EN.pdf) - 模型社区许可证

## 🚀 项目简介

Duix Mobile 是由硅基智能开源的移动端实时对话数字人SDK，具有以下特点：

- **跨平台支持**: iOS / Android / 平板 / 车载系统 / VR设备
- **实时交互**: 响应延迟低于120ms，支持流式音频
- **本地运行**: 无需联网，完全离线运行
- **高性能**: 适配手机、平板等移动设备
- **易集成**: 模块化设计，支持快速定制

## 🎯 应用场景

- 智能客服系统
- 虚拟医生/律师
- 虚拟陪伴应用
- 教育培训系统
- 政务服务大厅
- 银行金融服务
- 直播互动平台

## 🔗 相关链接

### 代码仓库
- [GitHub主仓库](https://github.com/duixcom/Duix-Mobile)
- [Gitee镜像](https://gitee.com/duix/Duix-Mobile)
- [GitCode镜像](https://gitcode.com/openguiji/duix-mobile)

### 官方资源
- [官方网站](https://www.duix.com)
- [演示视频](https://www.bilibili.com/video/BV1t2g7z3ERK/)
- [技术博客](https://github.blog)

## 📞 技术支持

### 联系方式
- **技术支持邮箱**: <EMAIL>
- **企业微信群**: 见项目README中的二维码

### 常见问题
如果您在使用过程中遇到问题，请先查看：
1. [Java环境要求](./Java环境要求.md) - 环境配置相关问题
2. [Android文档](../README.md) - Android开发相关问题
3. [GitHub Issues](https://github.com/duixcom/Duix-Mobile/issues) - 已知问题和解决方案

## 📝 贡献指南

我们欢迎社区贡献！如果您想为文档做出贡献：

1. Fork 项目仓库
2. 创建您的特性分支
3. 提交您的更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目遵循 [Guiji-duix.ai-sdk-1.0.3 Model Community License](../Guiji-duix.ai-sdk-1.0.3%20Model%20Community%20License_EN.pdf)。

---

*最后更新: 2025年1月*
*维护者: Duix Mobile 开发团队*
