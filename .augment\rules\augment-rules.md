---
type: "always_apply"
description: "duix.mobile Android项目开发规范"
---
# duix.mobile Android项目开发规范

作为 `duix.mobile` Android项目的开发代理，您必须严格遵守以下开发规范。

## I. 基础开发要求

### 1. 语言规范
- 所有回复、注释、文档必须使用中文，确保团队沟通的一致性
- 代码中的变量名、方法名可使用英文，但注释说明必须使用中文

### 2. UI设计统一性
- 项目必须统一使用 **Microsoft Fluent UI 2.0** 作为UI设计规范
- 所有界面组件、颜色、字体、间距等必须遵循Fluent UI 2.0的设计原则
- 禁止混用其他UI框架或自定义样式，确保整体视觉一致性
- 在实现UI组件时，优先查阅Fluent UI 2.0官方文档和设计指南

### 3. 代码架构与质量
- 严格遵循模块化开发原则，单一职责原则
- 禁止在单个文件中实现所有功能，必须进行合理的功能拆分和解耦
- 采用分层架构，明确区分业务逻辑层、数据访问层、UI层
- 优先考虑代码的可维护性、可扩展性和可测试性
- 遵循SOLID设计原则，确保代码质量

### 4. 文件创建规范
每个新建的源代码文件必须在文件顶部添加标准的中文功能说明注释：

```java
/**
 * 文件名: [具体文件名]
 * 功能描述: [详细描述该文件的主要功能和职责]
 * 创建日期: [YYYY-MM-DD]
 * 作者: [开发者信息]
 * 模块归属: [所属功能模块]
 */
```

### 5. 代码注释要求
- 所有方法、类、重要逻辑块必须添加清晰的中文注释
- 注释应说明"为什么这样做"而不仅仅是"做了什么"
- 复杂算法或业务逻辑必须提供详细的实现思路说明
- 公共API和接口必须提供完整的JavaDoc注释

### 6. 组件化开发意识
- 必须具备组件化思维，将功能封装为可复用的独立组件
- 考虑未来的拖拽布局需求，组件应具备良好的可配置性和灵活性
- 为后续功能扩展预留接口，支持插件化架构
- 组件间通信应通过明确定义的接口进行，避免紧耦合
- 每个组件应具备独立的生命周期管理

### 7. 开发流程规范
- 必须遵循"先思考、后编码"的原则
- 在编写代码前，必须先进行需求分析、架构设计、技术方案评估
- 禁止盲目修改代码，每次修改都应有明确的目标和预期结果
- 重大功能开发前必须先制定详细的开发计划和里程碑
- 使用任务管理工具进行开发进度跟踪

### 8. 技术环境统一
- 项目强制使用 **Java 17** 作为开发和运行环境
- 所有依赖库、构建工具、IDE配置都必须与Java 17兼容
- 禁止使用过时或不兼容的Java特性
- 统一使用Gradle作为构建工具，遵循Android项目标准结构

## II. 补充规范

### 9. 测试要求
- 每个新功能必须编写对应的单元测试
- 关键业务逻辑必须达到80%以上的测试覆盖率
- 集成测试和UI测试应覆盖主要用户场景
- 测试代码同样需要遵循代码质量规范和注释要求

### 10. 版本控制规范
- 提交信息必须使用中文，格式为：`[功能模块] 具体修改内容`
- 每次提交应包含完整的功能点，避免碎片化提交
- 重要功能开发应使用feature分支，完成后通过PR合并
- 提交前必须确保代码通过所有测试用例

### 11. 性能与安全
- 代码必须考虑Android平台的性能特点，避免内存泄漏和ANR
- 敏感数据处理必须遵循安全编码规范
- 网络请求、数据库操作等必须进行异常处理
- 图片、资源文件应进行适当的压缩和优化

### 12. 文档维护
- 重要功能模块必须维护对应的技术文档
- API接口变更必须及时更新文档
- 架构决策和重要技术选型必须记录决策过程和原因
- 使用Markdown格式编写文档，确保可读性

## III. 工具使用规范

### 13. 代码检索与分析
- 在修改现有代码前，必须使用 `codebase-retrieval` 工具深入了解相关代码结构
- 使用 `git-commit-retrieval` 工具查看历史变更，了解代码演进过程
- 对于复杂的代码修改，应先分析依赖关系和影响范围

### 14. 文件操作规范
- 使用 `str-replace-editor` 工具进行代码修改，禁止直接重写整个文件
- 使用 `save-file` 工具创建新文件
- 展示代码时必须使用 `<augment_code_snippet>` 标签格式

### 15. 任务管理
- 对于复杂开发任务，必须使用任务管理工具进行规划和跟踪
- 及时更新任务状态，确保开发进度透明化
- 每个任务完成后应进行总结和经验记录

### 16. 依赖管理
- 必须使用Gradle命令进行依赖管理，禁止手动编辑build.gradle文件
- 新增依赖前应评估其必要性和安全性
- 定期检查和更新依赖版本，确保安全性

## IV. Android特定规范

### 17. Android项目结构
- 严格遵循Android项目标准目录结构
- 资源文件命名必须遵循Android命名规范
- 使用合适的资源限定符支持多屏幕适配
- 模块划分应清晰，避免循环依赖

### 18. 生命周期管理
- 正确处理Activity、Fragment、Service等组件的生命周期
- 避免在生命周期方法中执行耗时操作
- 合理使用ViewModel和LiveData进行数据管理
- 注意内存泄漏问题，及时释放资源

### 19. 权限与安全
- 遵循最小权限原则，只申请必要的权限
- 正确处理运行时权限申请
- 敏感操作必须进行权限检查
- 数据传输和存储必须考虑安全性

### 20. 国际化支持
- 所有用户可见的文本必须使用字符串资源
- 为不同语言提供相应的资源文件
- 考虑不同地区的文化差异和使用习惯
- 布局应支持RTL（从右到左）语言

### 21. 性能优化
- 合理使用RecyclerView，避免ListView
- 图片加载使用合适的缓存策略
- 避免在主线程执行网络请求和数据库操作
- 使用ProGuard或R8进行代码混淆和优化

### 22. 错误处理与日志
- 建立统一的异常处理机制
- 合理使用日志级别，生产环境避免输出敏感信息
- 崩溃信息应能够帮助快速定位问题
- 网络异常、数据异常等应有友好的用户提示

## V. 开发环境与工具

### 23. 开发环境要求
- 使用最新稳定版本的Android Studio
- 配置统一的代码格式化规则
- 启用代码检查工具（Lint、SonarQube等）
- 使用统一的Git配置和提交规范

### 24. 调试与测试工具
- 充分利用Android Studio的调试功能
- 使用模拟器和真机进行充分测试
- 集成自动化测试工具
- 使用性能分析工具监控应用性能

### 25. 持续集成
- 配置自动化构建流程
- 代码提交触发自动化测试
- 自动化部署到测试环境
- 代码质量检查集成到CI/CD流程

## VI. 特殊注意事项

### 26. Windows环境适配
- 所有开发活动和命令执行都在Windows操作系统环境下进行
- 处理文件路径时遵循Windows约定
- 使用PowerShell作为默认命令行工具
- 注意Windows和Linux环境下的差异

### 27. 团队协作
- 代码审查是必须的流程，不允许直接合并到主分支
- 重要技术决策应通过团队讨论确定
- 定期进行代码重构和技术债务清理
- 知识分享和技术交流应制度化

### 28. 项目文档
- 项目根目录的wiki文件是重要的信息源
- 开发前必须仔细阅读项目文档
- 及时更新和维护项目文档
- 新功能开发应同步更新相关文档

### 29. 质量保证
- 代码提交前必须进行自测
- 单元测试覆盖率不得低于要求标准
- 集成测试应覆盖主要业务流程
- 性能测试应在不同设备上进行验证

### 30. 持续改进
- 定期回顾和优化开发流程
- 收集和分析用户反馈
- 技术栈升级应谨慎评估
- 建立技术债务管理机制
