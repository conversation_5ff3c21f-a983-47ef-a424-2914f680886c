# Duix Mobile 项目概览

## 🎯 项目定位

Duix Mobile 是一个**轻量级、纯离线**的移动端2D虚拟人解决方案，专为实时对话场景设计。项目由硅基智能开源，旨在为开发者提供高性能、易集成的数字人SDK。

## 🏗 技术架构

### 核心组件架构
```
┌─────────────────────────────────────────┐
│              应用层 (App Layer)           │
├─────────────────────────────────────────┤
│           SDK接口层 (SDK API)            │
├─────────────────────────────────────────┤
│         渲染引擎 (Render Engine)          │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │ DUIX Core   │  │ DUIXRenderer    │   │
│  │             │  │                 │   │
│  └─────────────┘  └─────────────────┘   │
├─────────────────────────────────────────┤
│        音频处理 (Audio Processing)        │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │ PCM Stream  │  │ WAV Player      │   │
│  │             │  │                 │   │
│  └─────────────┘  └─────────────────┘   │
├─────────────────────────────────────────┤
│         AI推理引擎 (AI Inference)         │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │    ONNX     │  │      NCNN       │   │
│  │             │  │                 │   │
│  └─────────────┘  └─────────────────┘   │
├─────────────────────────────────────────┤
│        底层渲染 (Native Rendering)        │
│  ┌─────────────┐  ┌─────────────────┐   │
│  │ OpenGL ES   │  │     C++17       │   │
│  │             │  │                 │   │
│  └─────────────┘  └─────────────────┘   │
└─────────────────────────────────────────┘
```

### 技术栈详情

#### 前端技术
- **Android**: Kotlin + Java 8
- **UI框架**: AndroidX + Material Design
- **渲染**: OpenGL ES + GLSurfaceView
- **视图绑定**: ViewBinding

#### 后端技术
- **AI推理**: ONNX Runtime + NCNN
- **音频处理**: PCM流处理 + WAV解码
- **图像处理**: OpenCV (可选)
- **并发处理**: C++ std::thread

#### Native层
- **语言**: C++17
- **构建系统**: CMake 3.18.1
- **架构支持**: ARM64-v8a, ARMeabi-v7a
- **标准库**: libc++

## 🔧 核心功能模块

### 1. 数字人渲染模块
```kotlin
// 核心渲染接口
interface RenderSink {
    fun onVideoFrame(imageFrame: ImageFrame)
}

// 默认渲染器
class DUIXRenderer : RenderSink {
    // 支持透明通道渲染
    // 实时帧率控制
    // GPU加速处理
}
```

### 2. 音频驱动模块
```kotlin
// PCM流式推送
duix.startPush()
duix.pushPcm(pcmData)
duix.stopPush()

// WAV文件播放
duix.playAudio(wavPath)
```

### 3. 动作控制模块
```kotlin
// 指定动作播放
duix.startMotion("打招呼", true)

// 随机动作播放
duix.startRandomMotion(true)
```

### 4. 模型管理模块
```kotlin
// 模型检查与下载
VirtualModelUtil.checkModel(context, modelName)
VirtualModelUtil.modelDownload(context, modelUrl, callback)
```

## 📊 性能指标

### 响应性能
| 指标 | 数值 | 说明 |
|------|------|------|
| 响应延迟 | < 120ms | 骁龙8 Gen2测试环境 |
| 帧率 | 30-60 FPS | 根据设备性能自适应 |
| 内存占用 | < 800MB | 数字人专用内存 |
| CPU占用 | < 50% | 8核心设备 |

### 兼容性
| 平台 | 支持版本 | 架构 |
|------|----------|------|
| Android | 7.0+ (API 24+) | ARM64, ARM32 |
| iOS | 待补充 | ARM64 |

## 🎨 数字人模型

### 模型格式
- **视频格式**: MP4 (H.264编码)
- **音频格式**: PCM 16kHz 单声道 16位
- **配置文件**: JSON格式
- **动作标注**: SpecialAction.json

### 预置模型
项目提供8个公有数字人模型：
1. **guilv0515** - 律师形象
2. **guilv3** - 法务顾问
3. **wuhao** - 商务人士
4. **siyao** - 客服代表
5. **696309955760197** - 女性形象1
6. **696303589556293** - 女性形象2
7. **696326678212677** - 女性形象3
8. **651686686687301** - 男性形象

### 自定义模型
- **录制要求**: 15秒-2分钟视频
- **制作周期**: 1-3个工作日
- **定制服务**: 联系技术支持

## 🔄 开发流程

### 1. 环境准备
```bash
# 检查Java版本
java -version  # 需要JDK 1.8

# 安装Android Studio
# 配置SDK和NDK
```

### 2. 项目集成
```gradle
// settings.gradle
include ':duix-sdk'

// app/build.gradle
dependencies {
    implementation project(":duix-sdk")
}
```

### 3. 初始化流程
```kotlin
// 1. 检查模型
if (!VirtualModelUtil.checkModel(context, modelName)) {
    VirtualModelUtil.modelDownload(context, modelUrl, callback)
}

// 2. 创建DUIX实例
val duix = DUIX(context, modelName, renderer) { event, msg, info ->
    when (event) {
        "init.ready" -> onInitSuccess()
        "init.error" -> onInitError()
    }
}

// 3. 初始化
duix.init()
```

### 4. 音频播放
```kotlin
// PCM流式播放
Thread {
    duix.startPush()
    inputStream.use { stream ->
        val buffer = ByteArray(320)
        while (stream.read(buffer) > 0) {
            duix.pushPcm(buffer)
        }
    }
    duix.stopPush()
}.start()
```

## 🛡 安全与隐私

### 数据安全
- **完全离线**: 无需网络连接，数据不上传
- **本地处理**: 所有AI推理在设备本地完成
- **隐私保护**: 符合GDPR和国内数据保护法规

### 代码安全
- **混淆保护**: 支持ProGuard代码混淆
- **签名验证**: 支持APK签名校验
- **权限最小化**: 仅申请必要的系统权限

## 📈 版本演进

### 当前版本 (4.0.2)
- ✅ PCM流式音频支持
- ✅ 动作区间精确控制
- ✅ 自定义音频播放器
- ✅ 模型下载管理工具

### 历史版本
- **3.0.5**: ARM32兼容性修复
- **3.0.4**: GL精度问题修复
- **3.0.3**: 本地渲染优化

### 未来规划
- 🔄 算法响应优化 (2025年8月)
- 🔄 更多预置模型
- 🔄 iOS版本完善
- 🔄 VR/AR支持

## 🤝 社区与生态

### 开源社区
- **GitHub**: 7.2k+ Stars, 1k+ Forks
- **贡献者**: 8位核心贡献者
- **Issue**: 45个开放问题

### 商业支持
- **技术支持**: 企业微信群
- **定制服务**: 私有化部署
- **培训服务**: 开发者培训

---

*文档版本: v1.0*
*最后更新: 2025年1月*
