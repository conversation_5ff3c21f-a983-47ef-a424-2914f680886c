# Duix Mobile项目 - Java环境要求

## 📋 概述

Duix Mobile 是一个移动端实时对话数字人SDK项目，支持Android和iOS平台。本文档详细说明了该项目的Java开发环境要求。

## ☕ Java版本要求

### 核心要求
- **Java 17 (推荐)** - 现代Android开发推荐版本，支持最新的Android Gradle Plugin
- **Java 8 (JDK 1.8)** - 项目代码兼容版本，但构建工具需要Java 17+
- **重要性**: Android Gradle Plugin 8.1.2+ 需要Java 17来运行构建过程

### 配置说明
项目在build.gradle中明确指定：
```gradle
compileOptions {
    sourceCompatibility JavaVersion.VERSION_1_8
    targetCompatibility JavaVersion.VERSION_1_8
}
```

## 🛠 Android开发环境

### IDE要求
- **Android Studio**: Giraffe 2022.3.1 Patch 2 或更高版本
- **推荐**: 使用最新稳定版本以获得最佳开发体验

### 构建工具版本
- **Android Gradle Plugin**: 8.1.2
- **Gradle版本**: 8.0
- **Kotlin版本**: 1.8.10

### Gradle配置
```gradle
// 项目级build.gradle
classpath 'com.android.tools.build:gradle:8.1.2'
classpath 'org.jetbrains.kotlin:kotlin-gradle-plugin:1.8.10'
```

## 📱 Android SDK配置

### SDK版本要求
| 配置项 | 版本 | 说明 |
|--------|------|------|
| 编译SDK版本 | 33-34 | compileSdk |
| 最低支持版本 | Android 7.0 (API 24) | minSdk |
| 目标SDK版本 | 33-34 | targetSdk |
| 构建工具版本 | 30.0.2 | buildToolsVersion |

### 支持的Android版本
- **最低支持**: Android 7.0 (API 24)
- **推荐**: Android 10+ 系统
- **目标**: Android 13-14 (API 33-34)

## 💻 硬件要求

### CPU架构支持
- **armeabi-v7a** (32位ARM)
- **arm64-v8a** (64位ARM)

### 设备性能要求
| 硬件组件 | 最低要求 | 推荐配置 |
|----------|----------|----------|
| CPU | 8核及以上 | 骁龙8 Gen2或同等性能 |
| 内存 | 8GB | 12GB或更高 |
| 数字人专用内存 | 800MB | 1GB或更高 |
| 存储空间 | 1GB可用空间 | 2GB或更高 |

### 性能说明
- 项目为实时数字人渲染，对性能要求较高
- 内存不足可能导致音频特征提取速度跟不上播放速度
- 建议在高性能设备上进行开发和测试

## 🔧 Native开发环境

### CMake配置
- **CMake版本**: 3.18.1
- **C++标准**: C++17
- **编译标志**: `-std=c++17`, `-fexceptions`

### NDK要求
```gradle
externalNativeBuild {
    cmake {
        abiFilters 'arm64-v8a', "armeabi-v7a"
        cppFlags "-std=c++17", "-fexceptions"
    }
}
```

## 📦 依赖库

### 核心依赖
- **ONNX**: 通用AI模型标准格式
- **NCNN**: 高性能神经网络计算框架（腾讯）
- **OpenGL ES**: Android图形渲染接口

### Android依赖
- **AndroidX**: 现代Android支持库
- **Kotlin**: 1.8.10版本
- **Material Design**: UI组件库

## 🚀 快速环境配置

### 1. 安装Java环境

#### 方法1: 使用winget安装Java 17 (推荐)
```powershell
# 安装Microsoft OpenJDK 17
winget install Microsoft.OpenJDK.17

# 验证安装
java -version
# 应该显示：openjdk version "17.0.16" 2025-07-15 LTS
```

#### 方法2: 手动下载安装
从 [Microsoft OpenJDK](https://docs.microsoft.com/java/openjdk/download) 下载并安装Java 17

### 2. Android Studio配置
1. 下载并安装Android Studio Giraffe 2022.3.1 Patch 2+
2. 安装Android SDK API 24, 33, 34
3. 安装NDK和CMake工具
4. 配置Gradle JVM为JDK 1.8

### 3. 项目构建和启动

#### 命令行构建 (推荐)
```powershell
# 设置Java 17环境并构建
$env:JAVA_HOME = "C:\Program Files\Microsoft\jdk-*********-hotspot"
.\gradlew.bat clean :test:assembleDebug --init-script init.gradle
```

#### 使用构建脚本
```powershell
# 运行自动化构建脚本
.\build.bat
```

#### Android Studio导入
1. 克隆项目到本地
2. 使用Android Studio打开项目
3. 等待Gradle同步完成
4. 检查SDK和NDK路径配置

#### 构建产物
- **APK位置**: `test/build/outputs/apk/debug/duix_mobile_test_debug_3.0.2.apk`
- **安装命令**: `adb install test/build/outputs/apk/debug/duix_mobile_test_debug_3.0.2.apk`

## ⚠️ 注意事项

### 兼容性警告
- **构建环境**: 需要Java 17来运行Android Gradle Plugin 8.1.2+
- **运行时兼容**: 应用代码仍然兼容Java 8语法
- **内存敏感**: PCM音频缓存在内存中，避免过长音频流
- **网络独立**: 核心功能完全本地运行，无需联网

### 常见问题及解决方案
1. **UnsatisfiedLinkError: library "libgjduix.so" not found**
   - 原因: native库构建失败或未打包
   - 解决: 确保CMake配置正确，使用CMake 3.22.1+版本

2. **init回调失败**: 检查模型路径和下载状态
3. **渲染黑屏**: 验证EGL配置和纹理视图设置
4. **PCM无播报**: 确认音频格式(16kHz单通道16位深)
5. **模型下载慢**: 可自建模型文件托管服务

### 构建成功验证
构建成功后，APK应包含以下native库：
- `lib/arm64-v8a/libgjduix.so` - 主要SDK库
- `lib/arm64-v8a/libncnn.so` - AI推理引擎
- `lib/arm64-v8a/libonnxruntime.so` - ONNX运行时
- `lib/armeabi-v7a/` 对应的32位版本

## 📚 相关文档

- [Android开发文档](./README.md)
- [iOS开发文档](../duix-ios/GJLocalDigitalDemo/README.md)
- [接口参考手册](../Interface%20Reference%20Handbook_EN.pdf)
- [项目主页](https://github.com/duixcom/Duix-Mobile)

## 📞 技术支持

如遇到环境配置问题，请联系：
- **邮箱**: <EMAIL>
- **技术支持群**: 见项目README中的企业微信二维码

---

*最后更新: 2025年1月*
