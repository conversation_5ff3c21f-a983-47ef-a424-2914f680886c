<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="138dp"
        android:background="@drawable/shape_solid_b3000000_radius_7"
        android:gravity="center"
        android:minWidth="138dp"
        android:orientation="vertical"
        android:paddingBottom="2dp">

        <ImageView
            android:id="@+id/iv_progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@mipmap/iv_progress" />

        <TextView
            android:id="@+id/tv_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:gravity="center"
            android:singleLine="true"
            android:text="@string/loading"
            android:layout_marginHorizontal="12dp"
            android:textColor="@color/white"
            android:textSize="12dp"
            android:textStyle="bold" />
    </LinearLayout>
</LinearLayout>
