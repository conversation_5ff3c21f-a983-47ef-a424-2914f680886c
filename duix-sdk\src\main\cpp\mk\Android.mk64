#/****************************************************************************
#*   Cartoonifier, for Android.
#*****************************************************************************
#*   by <PERSON><PERSON>, 5th Dec 2012 (<EMAIL>)
#*   http://www.shervinemami.info/
#*****************************************************************************
#*   Ch1 of the book "Mastering OpenCV with Practical Computer Vision Projects"
#*   Copyright Packt Publishing 2012.
#*   http://www.packtpub.com/cool-projects-with-opencv/book
#****************************************************************************/


LOCAL_PATH := $(call my-dir)


include $(CLEAR_VARS)



LOCAL_SRC_FILES  += src/kmatarm.cpp

LOCAL_ARM_NEON := true
LOCAL_MODULE := facedetect
LOCAL_LDLIBS +=  -llog -ldl -lm -lmediandk
LOCAL_LDLIBS += -ljnigraphics -fopenmp
LOCAL_CFLAGS += -fpermissive
LOCAL_CPPFLAGS += -fpermissive
#LOCAL_CFLAGS += -ftree-vectorizer-verbose=2
LOCAL_CPPFLAGS += -std=c++17
LOCAL_LDLIBS += -lstdc++

LOCAL_C_INCLUDES += $(LOCAL_PATH)
LOCAL_C_INCLUDES += include
LOCAL_C_INCLUDES += opencv-mobile-4.6.0-android/sdk/native/jni/include/
LOCAL_C_INCLUDES += ncnn-20221128-android-vulkan-shared/arm64-v8a/include/ncnn

include $(BUILD_SHARED_LIBRARY)
