# build.sh
# 在Linux下编译FFmpeg成功的脚本
# 注意Linux和windows的换行符\r\n不太一样，要转换（dos2unix）
#!/bin/sh
make clean
export NDK=~/work/android-ndk-r15c-linux-x86_64/android-ndk-r15c
export PREBUILT=$NDK/toolchains/arm-linux-androideabi-4.9/prebuilt
export PLATFORM=$NDK/platforms/android-21/arch-arm
export PREFIX=../fflib/free-arm-lite
build_one(){
./configure --target-os=android --prefix=$PREFIX \
--enable-cross-compile \
--enable-runtime-cpudetect \
--arch=arm \
--cross-prefix=$PREBUILT/linux-x86_64/bin/arm-linux-androideabi- \
--cc=$PREBUILT/linux-x86_64/bin/arm-linux-androideabi-gcc \
--nm=$PREBUILT/linux-x86_64/bin/arm-linux-androideabi-nm \
--sysroot=$PLATFORM \
--disable-gpl --disable-nonfree \
--enable-shared --enable-static --enable-small \
--disable-doc --disable-ffprobe --disable-ffplay --disable-debug \
--enable-jni \
--enable-mediacodec \
--disable-avdevice \
--enable-avcodec \
--enable-avformat \
--enable-avutil \
--enable-swresample \
--enable-swscale \
--disable-postproc \
--enable-avfilter \
--disable-avresample \
--disable-decoders \
--enable-decoder=aac \
--enable-decoder=aac_latm \
--enable-decoder=flv \
--enable-decoder=h264 \
--enable-decoder=mp3* \
--enable-decoder=vp6f \
--enable-decoder=flac \
--enable-decoder=hevc \
--enable-decoder=vp8 \
--enable-decoder=vp9 \
--enable-decoder=amrnb \
--enable-decoder=amrwb \
--enable-decoder=mjpeg \
--enable-decoder=png \
--enable-decoder=h264_mediacodec \
--enable-hwaccel=h264_mediacodec \
--disable-encoders \
--enable-encoder=aac \
--enable-encoder=h264 \
--enable-encoder=hevc \
--enable-encoder=png \
--enable-encoder=mjpeg \
--disable-demuxers \
--enable-demuxer=aac \
--enable-demuxer=concat \
--enable-demuxer=data \
--enable-demuxer=flv \
--enable-demuxer=hls \
--enable-demuxer=live_flv \
--enable-demuxer=mov \
--enable-demuxer=mp3 \
--enable-demuxer=mpegps \
--enable-demuxer=mpegts \
--enable-demuxer=mpegvideo \
--enable-demuxer=flac \
--enable-demuxer=hevc \
--enable-demuxer=webm_dash_manifest \
--enable-demuxer=rtsp \
--enable-demuxer=rtp \
--enable-demuxer=h264 \
--enable-demuxer=mp4 \
--enable-demuxer=image2 \
--disable-muxers \
--enable-muxer=rtsp \
--enable-muxer=rtp \
--enable-muxer=flv \
--enable-muxer=h264 \
--enable-muxer=mp4 \
--enable-muxer=hevc \
--enable-muxer=image2 \
--disable-parsers \
--enable-parser=aac \
--enable-parser=aac_latm \
--enable-parser=h264 \
--enable-parser=flac \
--enable-parser=hevc \
--enable-protocols \
--enable-protocol=async \
--disable-protocol=bluray \
--disable-protocol=concat \
--disable-protocol=crypto \
--disable-protocol=ffrtmpcrypt \
--enable-protocol=ffrtmphttp \
--disable-protocol=gopher \
--disable-protocol=icecast \
--disable-protocol=librtmp* \
--disable-protocol=libssh \
--disable-protocol=md5 \
--disable-protocol=mmsh \
--disable-protocol=mmst \
--disable-protocol=rtmp* \
--enable-protocol=rtmp \
--enable-protocol=rtmpt \
--disable-protocol=rtp \
--disable-protocol=sctp \
--disable-protocol=srtp \
--disable-protocol=subfile \
--disable-protocol=unix \
--disable-indevs \
--disable-outdevs \
--disable-stripping \
--enable-asm
}
build_one
make
make install
