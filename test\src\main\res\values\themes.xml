<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Fluent UI 2.0 主题 -->
    <style name="Theme.DUIX.Test" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowBackground">@color/fluent_background</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowTranslucentStatus">true</item>
        
        <!-- Fluent 颜色映射 -->
        <item name="colorPrimary">@color/fluent_primary</item>
        <item name="colorPrimaryDark">@color/fluent_primary_pressed</item>
        <item name="colorAccent">@color/fluent_accent</item>
    </style>
    
    <!-- Fluent 按钮样式 -->
    <style name="Widget.Fluent.Button" parent="Widget.AppCompat.Button">
        <item name="android:textSize">14sp</item>
        <item name="android:paddingStart">20dp</item>
        <item name="android:paddingEnd">20dp</item>
        <item name="android:paddingTop">8dp</item>
        <item name="android:paddingBottom">8dp</item>
        <item name="android:minHeight">32dp</item>
        <item name="android:background">@drawable/fluent_button_primary</item>
        <item name="android:textColor">@color/fluent_neutral_100</item>
        <item name="android:stateListAnimator">@null</item>
        <item name="android:elevation">0dp</item>
    </style>
    
    <style name="Widget.Fluent.Button.Secondary" parent="Widget.AppCompat.Button">
        <item name="android:textSize">14sp</item>
        <item name="android:paddingStart">20dp</item>
        <item name="android:paddingEnd">20dp</item>
        <item name="android:paddingTop">8dp</item>
        <item name="android:paddingBottom">8dp</item>
        <item name="android:minHeight">32dp</item>
        <item name="android:background">@drawable/fluent_button_secondary</item>
        <item name="android:textColor">@color/fluent_neutral_20</item>
        <item name="android:stateListAnimator">@null</item>
        <item name="android:elevation">0dp</item>
    </style>
    
    <style name="Widget.Fluent.Button.Text" parent="Widget.AppCompat.Button.Borderless">
        <item name="android:textSize">14sp</item>
        <item name="android:paddingStart">12dp</item>
        <item name="android:paddingEnd">12dp</item>
        <item name="android:paddingTop">8dp</item>
        <item name="android:paddingBottom">8dp</item>
        <item name="android:minHeight">32dp</item>
        <item name="android:textColor">@color/fluent_primary</item>
        <item name="android:stateListAnimator">@null</item>
        <item name="android:elevation">0dp</item>
    </style>
    
    <!-- Fluent 输入框样式 -->
    <style name="Widget.Fluent.TextInputLayout" parent="Widget.Design.TextInputLayout">
        <item name="boxStrokeColor">@color/fluent_neutral_60</item>
        <item name="hintTextColor">@color/fluent_neutral_40</item>
        <item name="boxCornerRadiusBottomEnd">4dp</item>
        <item name="boxCornerRadiusBottomStart">4dp</item>
        <item name="boxCornerRadiusTopEnd">4dp</item>
        <item name="boxCornerRadiusTopStart">4dp</item>
        <item name="boxStrokeWidth">1dp</item>
        <item name="boxStrokeWidthFocused">2dp</item>
    </style>
    
    <!-- Fluent 卡片样式 -->
    <style name="Widget.Fluent.CardView" parent="CardView">
        <item name="cardCornerRadius">8dp</item>
        <item name="cardElevation">1dp</item>
        <item name="cardBackgroundColor">@color/fluent_surface</item>
        <item name="contentPadding">16dp</item>
    </style>
    
    <!-- Fluent 开关样式 -->
    <style name="Widget.Fluent.Switch" parent="Widget.AppCompat.CompoundButton.Switch">
        <item name="thumbTint">@color/fluent_neutral_100</item>
        <item name="trackTint">@color/fluent_neutral_80</item>
    </style>
</resources>