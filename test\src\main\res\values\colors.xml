<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Fluent UI 2.0 颜色系统 -->
    
    <!-- 主要颜色 -->
    <color name="fluent_primary">#0078D4</color>
    <color name="fluent_primary_hover">#106EBE</color>
    <color name="fluent_primary_pressed">#005A9E</color>
    
    <!-- 中性颜色 -->
    <color name="fluent_neutral_100">#FFFFFF</color>
    <color name="fluent_neutral_98">#FAFAFA</color>
    <color name="fluent_neutral_96">#F5F5F5</color>
    <color name="fluent_neutral_94">#F0F0F0</color>
    <color name="fluent_neutral_92">#E8E8E8</color>
    <color name="fluent_neutral_90">#E1E1E1</color>
    <color name="fluent_neutral_80">#CCCCCC</color>
    <color name="fluent_neutral_70">#B3B3B3</color>
    <color name="fluent_neutral_60">#999999</color>
    <color name="fluent_neutral_50">#808080</color>
    <color name="fluent_neutral_40">#666666</color>
    <color name="fluent_neutral_30">#4D4D4D</color>
    <color name="fluent_neutral_20">#333333</color>
    <color name="fluent_neutral_10">#1A1A1A</color>
    <color name="fluent_neutral_0">#000000</color>
    
    <!-- 语义颜色 -->
    <color name="fluent_success">#107C10</color>
    <color name="fluent_success_background">#DFF6DD</color>
    <color name="fluent_warning">#FF8C00</color>
    <color name="fluent_warning_background">#FFF4CE</color>
    <color name="fluent_error">#D13438</color>
    <color name="fluent_error_background">#FDE7E9</color>
    <color name="fluent_info">#0078D4</color>
    <color name="fluent_info_background">#D1E7FF</color>
    
    <!-- 强调色 -->
    <color name="fluent_accent">#0078D4</color>
    <color name="fluent_accent_light">#40E0FF</color>
    <color name="fluent_accent_dark">#005A9E</color>
    
    <!-- 背景和表面 -->
    <color name="fluent_background">#FAFAFA</color>
    <color name="fluent_surface">#FFFFFF</color>
    <color name="fluent_surface_secondary">#F5F5F5</color>
    <color name="fluent_surface_tertiary">#F0F0F0</color>
    
    <!-- 透明度颜色 -->
    <color name="fluent_overlay_light">#66FFFFFF</color>
    <color name="fluent_overlay_dark">#66000000</color>
    <color name="fluent_scrim">#4D000000</color>
    <color name="md_theme_surface_alpha_60">#99FFFFFF</color>
    <color name="md_theme_surface_alpha_80">#CCFFFFFF</color>
    <color name="md_theme_outlineVariant">@color/fluent_neutral_90</color>
    
    <!-- 兼容性颜色 -->
    <color name="black">@color/fluent_neutral_0</color>
    <color name="white">@color/fluent_neutral_100</color>
    <color name="primary">@color/fluent_primary</color>
    <color name="primary_dark">@color/fluent_primary_pressed</color>
    <color name="primary_99">#990078D4</color>
    <color name="accent">@color/fluent_accent</color>
    <color name="transparent_black">@color/fluent_overlay_dark</color>
    <color name="button_disabled">@color/fluent_neutral_90</color>
    <color name="card_background">@color/fluent_surface</color>
    <color name="divider">@color/fluent_neutral_90</color>
</resources>