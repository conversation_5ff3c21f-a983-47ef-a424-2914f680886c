plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
}

android {
    namespace 'ai.guiji.duix.test'
    compileSdk 34

    defaultConfig {
        applicationId "ai.guiji.duix.test"
        minSdk 24
        targetSdk 34
        versionCode 2
        versionName "3.0.2"
    }

    signingConfigs {
        release {
            storeFile file('../demo.jks')
            storePassword '123456'
            keyAlias 'demo'
            keyPassword '123456'
        }
    }

    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release

            applicationVariants.all { variant ->
                variant.outputs.all {
                    outputFileName = "duix_mobile_test_${buildType.name}_${defaultConfig.versionName}.apk"
                }
            }
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    buildFeatures {
        viewBinding true
    }
    composeOptions {
        kotlinCompilerExtensionVersion '1.4.3'
    }
    packaging {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
    }

    lint {
        baseline = file("lint-baseline.xml")
    }
    lintOptions{
        checkReleaseBuilds false
    }
}

dependencies {

    implementation fileTree(include: ['*.jar', '*.aar'], dir: 'libs')
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.appcompat:appcompat:1.2.0'
    implementation 'com.google.android.material:material:1.4.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.1'
    implementation "androidx.activity:activity:1.3.0"
    implementation "androidx.fragment:fragment:1.3.0"

    implementation 'com.github.bumptech.glide:glide:4.12.0'

    implementation project(":duix-sdk")
    implementation 'com.squareup.okhttp3:okhttp:4.10.0'
}