// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    repositories {
        // 国内镜像优先
        maven { url 'https://maven.aliyun.com/repository/public/' }
        maven { url 'https://maven.aliyun.com/repository/central' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        maven { url 'https://mirrors.cloud.tencent.com/nexus/repository/maven-public/' }
        maven { url 'https://repo.huaweicloud.com/repository/maven/' }
        // 备用仓库
        maven { url 'https://jitpack.io' }
        maven { url 'https://repo1.maven.org/maven2/' }
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.1.2'
        classpath 'org.jetbrains.kotlin:kotlin-gradle-plugin:1.8.10'
    }
}

allprojects {
    repositories {
        // 国内镜像优先
        maven { url 'https://maven.aliyun.com/repository/public/' }
        maven { url 'https://maven.aliyun.com/repository/central' }
        maven { url 'https://maven.aliyun.com/repository/google' }
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        maven { url 'https://mirrors.cloud.tencent.com/nexus/repository/maven-public/' }
        maven { url 'https://repo.huaweicloud.com/repository/maven/' }
        // 备用仓库
        maven { url 'https://jitpack.io' }
        maven { url 'https://repo1.maven.org/maven2/' }
        google()
        mavenCentral()
    }
}

ext {
    compileSdkVersion = 33
    buildToolsVersion = '30.0.2'
    minSdkVersion = 24
    targetSdkVersion = 33
    versionCode = 2
    versionName = "0.0.2"
}
