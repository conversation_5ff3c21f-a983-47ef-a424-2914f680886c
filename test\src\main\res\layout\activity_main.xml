<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/fluent_background">

    <!-- Fluent UI 2.0 顶部栏 -->
    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/fluent_primary"
        android:elevation="0dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="48dp"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:paddingBottom="20dp">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/app_name"
                android:textColor="@color/fluent_neutral_100"
                android:textSize="28sp"
                android:textStyle="normal"
                android:fontFamily="sans-serif-medium"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_sdk_version"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textColor="@color/fluent_neutral_100"
                android:textSize="14sp"
                android:alpha="0.8"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_title" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.appbar.AppBarLayout>

    <!-- 主内容区域 -->
    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipToPadding="false"
        android:padding="20dp"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <!-- 配置卡片 -->
            <androidx.cardview.widget.CardView
                android:id="@+id/configCard"
                style="@style/Widget.Fluent.CardView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/tvDownloadTips"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/main_download_tips"
                        android:textColor="@color/fluent_neutral_20"
                        android:textSize="20sp"
                        android:fontFamily="sans-serif-medium"
                        app:layout_constraintTop_toTopOf="parent" />

                    <!-- 基础配置输入框 -->
                    <EditText
                        android:id="@+id/etBaseConfig"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:hint="@string/base_config_url"
                        android:text="https://github.com/duixcom/Duix-Mobile/releases/download/v1.0.0/gj_dh_res.zip"
                        android:textSize="14sp"
                        android:textColor="@color/fluent_neutral_20"
                        android:background="@color/fluent_surface"
                        android:padding="12dp"
                        app:layout_constraintTop_toBottomOf="@+id/tvDownloadTips" />

                    <!-- 模型URL输入框 -->
                    <EditText
                        android:id="@+id/etUrl"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:layout_marginEnd="12dp"
                        android:hint="@string/model_url"
                        android:text="https://github.com/duixcom/Duix-Mobile/releases/download/v1.0.0/bendi3_20240518.zip"
                        android:textSize="14sp"
                        android:textColor="@color/fluent_neutral_20"
                        android:background="@color/fluent_surface"
                        android:padding="12dp"
                        app:layout_constraintEnd_toStartOf="@+id/btnMoreModel"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/etBaseConfig" />

                    <!-- 更多模型按钮 -->
                    <Button
                        android:id="@+id/btnMoreModel"
                        style="@style/Widget.Fluent.Button.Secondary"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/more"
                        app:layout_constraintBottom_toBottomOf="@+id/etUrl"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="@+id/etUrl" />

                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.cardview.widget.CardView>

            <!-- 播放按钮 -->
            <Button
                android:id="@+id/btnPlay"
                style="@style/Widget.Fluent.Button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:text="@string/play"
                android:textSize="16sp"
                android:paddingTop="12dp"
                android:paddingBottom="12dp"
                app:layout_constraintTop_toBottomOf="@+id/configCard" />

            <!-- 设置卡片 -->
            <androidx.cardview.widget.CardView
                android:id="@+id/settingsCard"
                style="@style/Widget.Fluent.CardView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                app:layout_constraintTop_toBottomOf="@+id/btnPlay">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/tvSettings"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="设置"
                        android:textColor="@color/fluent_neutral_20"
                        android:textSize="20sp"
                        android:fontFamily="sans-serif-medium"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/switchDebug"
                        style="@style/Widget.Fluent.Switch"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:text="@string/debug_message"
                        android:textColor="@color/fluent_neutral_40"
                        android:textSize="14sp"
                        app:layout_constraintTop_toBottomOf="@+id/tvSettings" />

                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.cardview.widget.CardView>

            <!-- 底部信息 -->
            <TextView
                android:id="@+id/tvFooter"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:gravity="center"
                android:text="Duix Mobile for Android SDK"
                android:textColor="@color/fluent_neutral_60"
                android:textSize="12sp"
                app:layout_constraintTop_toBottomOf="@+id/settingsCard" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>

</androidx.coordinatorlayout.widget.CoordinatorLayout>