# Fluent UI 2.0 组件使用指南

## 🎨 颜色使用

### 主要颜色
```xml
<!-- 在布局文件中使用 -->
android:background="@color/fluent_primary"
android:textColor="@color/fluent_neutral_100"

<!-- 在代码中使用 -->
ContextCompat.getColor(context, R.color.fluent_primary)
```

### 语义颜色
```xml
<!-- 成功状态 -->
android:textColor="@color/fluent_success"
android:background="@color/fluent_success_background"

<!-- 错误状态 -->
android:textColor="@color/fluent_error"
android:background="@color/fluent_error_background"
```

## 🔘 按钮组件

### 主要按钮
```xml
<com.google.android.material.button.MaterialButton
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:text="主要操作"
    style="@style/Widget.Fluent.Button" />
```

### 次要按钮
```xml
<com.google.android.material.button.MaterialButton
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:text="次要操作"
    style="@style/Widget.Fluent.Button.Secondary" />
```

### 文本按钮
```xml
<com.google.android.material.button.MaterialButton
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:text="文本操作"
    style="@style/Widget.Fluent.Button.Text" />
```

## 📝 输入框组件

### 标准输入框
```xml
<com.google.android.material.textfield.TextInputLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    style="@style/Widget.Fluent.TextInputLayout"
    android:hint="请输入内容">

    <com.google.android.material.textfield.TextInputEditText
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

</com.google.android.material.textfield.TextInputLayout>
```

## 🃏 卡片组件

### 标准卡片
```xml
<com.google.android.material.card.MaterialCardView
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    style="@style/Widget.Fluent.CardView">

    <!-- 卡片内容 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="卡片标题"
            android:textSize="18sp"
            android:textColor="@color/fluent_neutral_20" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="卡片内容描述"
            android:textSize="14sp"
            android:textColor="@color/fluent_neutral_40" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
```

## 💬 对话框组件

### 标准对话框
```kotlin
MaterialAlertDialogBuilder(context, R.style.ThemeOverlay_Fluent_MaterialAlertDialog)
    .setTitle("对话框标题")
    .setMessage("对话框内容描述")
    .setPositiveButton("确认") { dialog, _ ->
        // 确认操作
        dialog.dismiss()
    }
    .setNegativeButton("取消") { dialog, _ ->
        dialog.dismiss()
    }
    .show()
```

## 🎨 自定义样式示例

### 创建自定义按钮样式
```xml
<style name="Widget.App.Button.Custom" parent="Widget.Fluent.Button">
    <item name="backgroundTint">@color/fluent_accent</item>
    <item name="android:textColor">@color/fluent_neutral_100</item>
    <item name="cornerRadius">12dp</item>
</style>
```

### 创建自定义卡片样式
```xml
<style name="Widget.App.CardView.Elevated" parent="Widget.Fluent.CardView">
    <item name="cardElevation">8dp</item>
    <item name="cardCornerRadius">16dp</item>
    <item name="strokeWidth">0dp</item>
</style>
```

## 🌙 深色主题支持

### 在 values-night 目录下创建对应的颜色资源
```xml
<!-- values-night/colors.xml -->
<color name="fluent_background">@color/fluent_background_dark</color>
<color name="fluent_surface">@color/fluent_surface_dark</color>
```

## 📱 响应式设计

### 不同屏幕尺寸的适配
```xml
<!-- values-sw600dp/dimens.xml -->
<dimen name="fluent_button_padding_horizontal">24dp</dimen>
<dimen name="fluent_card_margin">16dp</dimen>

<!-- values/dimens.xml -->
<dimen name="fluent_button_padding_horizontal">20dp</dimen>
<dimen name="fluent_card_margin">8dp</dimen>
```

## ⚡ 性能优化建议

1. **使用矢量图标**: 优先使用Vector Drawable
2. **合理使用阴影**: 避免过度使用elevation
3. **颜色缓存**: 在代码中缓存常用颜色值
4. **布局优化**: 减少嵌套层级

## 🔧 迁移指南

### 从Material Design 3迁移
1. 替换颜色引用: `md_theme_*` → `fluent_*`
2. 更新样式引用: `Widget.App.*` → `Widget.Fluent.*`
3. 调整圆角大小: 通常从8dp改为4dp
4. 更新字体引用: 添加`android:fontFamily="@font/fluent_system_font"`

### 常见问题解决
- **颜色不显示**: 检查颜色资源是否正确引用
- **样式不生效**: 确认主题中已应用对应样式
- **字体显示异常**: 验证字体资源文件是否存在

---

**更新时间**: 2024年1月
**适用版本**: Duix Android SDK v3.0.2+
