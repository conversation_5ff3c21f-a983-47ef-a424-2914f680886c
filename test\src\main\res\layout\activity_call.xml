<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true">

    <!-- 背景图片 -->
    <ImageView
        android:id="@+id/iv_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:src="@drawable/gradient_background" />

    <!-- 数字人渲染视图 -->
    <ai.guiji.duix.sdk.client.render.DUIXTextureView
        android:id="@+id/glTextureView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <!-- 调试信息 -->
    <TextView
        android:id="@+id/tvDebug"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:background="@color/fluent_overlay_dark"
        android:padding="16dp"
        android:textColor="@color/fluent_neutral_100"
        android:textSize="12sp"
        android:fontFamily="monospace"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 右上角控制面板 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/controlPanel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="20dp"
        app:cardBackgroundColor="@color/fluent_overlay_light"
        app:cardCornerRadius="12dp"
        app:cardElevation="0dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="12dp">

            <androidx.appcompat.widget.SwitchCompat
                android:id="@+id/switchMute"
                style="@style/Widget.Fluent.Switch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:enabled="false"
                android:text="@string/mute"
                android:textColor="@color/fluent_neutral_20"
                android:textSize="14sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>

    <!-- 左上角动作面板 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/motionPanel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="20dp"
        app:cardBackgroundColor="@color/fluent_overlay_light"
        app:cardCornerRadius="12dp"
        app:cardElevation="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="16dp">

            <TextView
                android:id="@+id/tvMotionTips"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/support_actions"
                android:textColor="@color/fluent_neutral_20"
                android:textSize="16sp"
                android:fontFamily="sans-serif-medium"
                android:visibility="invisible"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvMotion"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:orientation="vertical"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvMotionTips" />

            <Button
                android:id="@+id/btnRandomMotion"
                style="@style/Widget.Fluent.Button.Secondary"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="@string/random_play_action"
                android:textSize="14sp"
                android:visibility="invisible"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/rvMotion" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>

    <!-- 底部控制面板 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/bottomControlPanel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="20dp"
        app:cardBackgroundColor="@color/fluent_overlay_light"
        app:cardCornerRadius="16dp"
        app:cardElevation="0dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="20dp">

            <!-- 停止按钮 -->
            <Button
                android:id="@+id/btnStopPlay"
                style="@style/Widget.Fluent.Button.Text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:enabled="false"
                android:text="@string/play_stop"
                android:textSize="14sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- WAV播放按钮 -->
            <Button
                android:id="@+id/btnPlayWAV"
                style="@style/Widget.Fluent.Button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="8dp"
                android:enabled="false"
                android:text="@string/play_wav"
                android:textSize="14sp"
                app:layout_constraintEnd_toStartOf="@+id/btnPlayPCM"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/btnStopPlay" />

            <!-- PCM播放按钮 -->
            <Button
                android:id="@+id/btnPlayPCM"
                style="@style/Widget.Fluent.Button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:enabled="false"
                android:text="@string/play_pcm"
                android:textSize="14sp"
                app:layout_constraintBottom_toBottomOf="@+id/btnPlayWAV"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/btnPlayWAV"
                app:layout_constraintTop_toTopOf="@+id/btnPlayWAV" />

            <!-- 录音按钮 -->
            <Button
                android:id="@+id/btnRecord"
                style="@style/Widget.Fluent.Button.Secondary"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:enabled="false"
                android:text="@string/record"
                android:textSize="14sp"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@+id/btnPlayWAV" />

            <!-- AI提示 -->
            <TextView
                android:id="@+id/tvAITips"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="@string/ai_tips"
                android:textColor="@color/fluent_neutral_40"
                android:textSize="14sp"
                android:gravity="center"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/btnPlayWAV" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>
</androidx.constraintlayout.widget.ConstraintLayout>