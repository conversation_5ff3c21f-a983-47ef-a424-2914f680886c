<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:fitsSystemWindows="true"
    android:paddingHorizontal="16dp"
    android:paddingVertical="16dp">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layoutFrame"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent">

        <TextView
            android:id="@+id/tvTouch"
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:paddingVertical="12dp"
            android:clickable="true"
            android:background="@drawable/selector_60_primary"
            android:gravity="center"
            android:text="@string/touch_me_record"
            android:textColor="@color/white"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>

